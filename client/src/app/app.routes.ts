import { Routes } from '@angular/router';
import { ProposalResolver } from './resolvers';
import { authGuard } from './guards';

export const routes: Routes = [
  { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
  {
    path: 'sign-in',
    loadComponent: () =>
      import('./pages/sign-in/sign-in.component').then(
        (m) => m.SignInComponent,
      ),
  },
  {
    path: 'p/:id',
    loadComponent: () =>
      import('./pages/proposal-details/proposal-details.component').then(
        (m) => m.ProposalDetailsComponent,
      ),
    resolve: {
      proposal: ProposalResolver,
    },
  },
  {
    path: 'proposal-editor',
    canActivate: [authGuard],
    loadComponent: () =>
      import('./pages/proposal-editor/proposal-editor.component').then(
        (m) => m.ProposalEditorComponent,
      ),
  },
  {
    path: 'dashboard',
    canActivate: [authGuard],
    loadComponent: () =>
      import('./pages/dashboard/dashboard.component').then(
        (m) => m.DashboardComponent,
      ),
  },
  {
    path: 'not-found',
    loadComponent: () =>
      import('./pages/not-found/not-found.component').then(
        (m) => m.NotFoundComponent,
      ),
  },
  {
    path: 'successful-payment',
    loadComponent: () =>
      import('./pages/successful-payment/successful-payment.component').then(
        (m) => m.SuccessfulPaymentComponent,
      ),
  },
  { path: '**', redirectTo: 'not-found' },
];
